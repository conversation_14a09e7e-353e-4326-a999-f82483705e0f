import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/verify_two_factor_usecase.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/services/biometric_service.dart';
import '../../view_model/custom_exception.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state and operations
///
/// This BLoC handles all authentication-related events and state changes
/// following clean architecture principles and the BLoC pattern.
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final VerifyTwoFactorUseCase _verifyTwoFactorUseCase;
  final AuthRepository _authRepository;
  final BiometricService? _biometricService;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required VerifyTwoFactorUseCase verifyTwoFactorUseCase,
    required AuthRepository authRepository,
    BiometricService? biometricService,
  })  : _loginUseCase = loginUseCase,
        _logoutUseCase = logoutUseCase,
        _verifyTwoFactorUseCase = verifyTwoFactorUseCase,
        _authRepository = authRepository,
        _biometricService = biometricService,
        super(AuthInitial()) {
    // Register event handlers
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthBiometricLoginRequested>(_onBiometricLoginRequested);
    on<AuthTwoFactorVerificationRequested>(_onTwoFactorVerificationRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthGlobalLogoutRequested>(_onGlobalLogoutRequested);
    on<AuthStatusCheckRequested>(_onStatusCheckRequested);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);
    on<AuthErrorCleared>(_onErrorCleared);
    on<AuthUserProfileUpdateRequested>(_onUserProfileUpdateRequested);
    on<AuthSessionTimeoutOccurred>(_onSessionTimeoutOccurred);
    on<AuthBiometricSetupRequested>(_onBiometricSetupRequested);
  }

  /// Handles email/password login requests
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading(message: 'Signing in...'));

    try {
      final result = await _loginUseCase.call(event.email, event.password);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(
          user: result.user!,
          accessToken: result.accessToken,
        ));
      } else if (result.twoFactorRefCode != null) {
        emit(AuthTwoFactorRequired(
          refCode: result.twoFactorRefCode!,
          message: 'Please enter the code sent to your authenticator app/SMS',
        ));
      } else {
        emit(AuthError(
          message: result.error ?? 'Login failed',
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      emit(AuthError(
        message:
            e is CustomException ? e.message : 'An unexpected error occurred',
      ));
    }
  }

  /// Handles biometric login requests
  Future<void> _onBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (_biometricService == null) {
      emit(AuthError(message: 'Biometric authentication not available'));
      return;
    }

    emit(AuthBiometricLoading(email: event.email));

    try {
      // First check if biometric authentication is available and enrolled
      final isAvailable = await _biometricService.isAvailable();
      if (!isAvailable) {
        emit(AuthError(message: 'Biometric authentication not available'));
        return;
      }

      final isEnrolled = await _biometricService.isEnrolled();
      if (!isEnrolled) {
        emit(AuthError(message: 'No biometric credentials enrolled'));
        return;
      }

      // Perform biometric authentication
      final biometricResult = await _biometricService.authenticate(
        reason: event.reason,
      );

      if (biometricResult.success) {
        // Use biometric login through repository
        final result = await _authRepository.loginWithBiometric(event.email);

        if (result.success && result.user != null) {
          emit(AuthAuthenticated(
            user: result.user!,
            accessToken: result.accessToken,
          ));
        } else if (result.twoFactorRefCode != null) {
          emit(AuthTwoFactorRequired(
            refCode: result.twoFactorRefCode!,
            message: 'Please enter the code sent to your authenticator app/SMS',
          ));
        } else {
          emit(AuthError(
            message: result.error ?? 'Biometric login failed',
          ));
        }
      } else {
        emit(AuthError(
          message: biometricResult.failure?.message ??
              'Biometric authentication failed',
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Biometric login error: $e');
      }
      emit(AuthError(
        message: e is CustomException
            ? e.message
            : 'Biometric authentication failed',
      ));
    }
  }

  /// Handles two-factor authentication verification
  Future<void> _onTwoFactorVerificationRequested(
    AuthTwoFactorVerificationRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading(message: 'Verifying code...'));

    try {
      final result =
          await _verifyTwoFactorUseCase.call(event.refCode, event.code);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(
          user: result.user!,
          accessToken: result.accessToken,
        ));
      } else {
        emit(AuthError(
          message: result.error ?? 'Two-factor verification failed',
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Two-factor verification error: $e');
      }
      emit(AuthError(
        message: e is CustomException ? e.message : 'Verification failed',
      ));
    }
  }

  /// Handles logout requests
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLogoutLoading());

    try {
      await _logoutUseCase.call();
      emit(AuthUnauthenticated(message: 'Logged out successfully'));
    } catch (e) {
      if (kDebugMode) {
        print('Logout error: $e');
      }
      // Even if logout fails, consider user as logged out locally
      emit(AuthUnauthenticated(message: 'Logged out'));
    }
  }

  /// Handles global logout requests
  Future<void> _onGlobalLogoutRequested(
    AuthGlobalLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLogoutLoading(isGlobalLogout: true));

    try {
      await _authRepository.globalLogout();
      emit(AuthUnauthenticated(message: 'Logged out from all devices'));
    } catch (e) {
      if (kDebugMode) {
        print('Global logout error: $e');
      }
      emit(AuthError(
        message: e is CustomException ? e.message : 'Global logout failed',
      ));
    }
  }

  /// Handles authentication status checks
  Future<void> _onStatusCheckRequested(
    AuthStatusCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated) {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          emit(AuthAuthenticated(user: user));
        } else {
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      if (kDebugMode) {
        print('Status check error: $e');
      }
      emit(AuthUnauthenticated());
    }
  }

  /// Handles token refresh requests
  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthTokenRefreshing());

    try {
      final newAccessToken = await _authRepository.refreshToken();
      final user = await _authRepository.getCurrentUser();

      if (user != null) {
        emit(AuthAuthenticated(
          user: user,
          accessToken: newAccessToken,
        ));
      } else {
        emit(AuthUnauthenticated(message: 'Session expired'));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Token refresh error: $e');
      }
      emit(AuthUnauthenticated(message: 'Session expired'));
    }
  }

  /// Handles error clearing
  void _onErrorCleared(
    AuthErrorCleared event,
    Emitter<AuthState> emit,
  ) {
    emit(AuthUnauthenticated());
  }

  /// Handles user profile updates
  Future<void> _onUserProfileUpdateRequested(
    AuthUserProfileUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null && state is AuthAuthenticated) {
        final currentState = state as AuthAuthenticated;
        emit(AuthAuthenticated(
          user: user,
          accessToken: currentState.accessToken,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Profile update error: $e');
      }
      // Don't emit error for profile updates, just log it
    }
  }

  /// Handles session timeout
  void _onSessionTimeoutOccurred(
    AuthSessionTimeoutOccurred event,
    Emitter<AuthState> emit,
  ) {
    emit(AuthUnauthenticated(
      message: event.message ?? 'Session expired. Please log in again.',
    ));
  }

  /// Handles biometric setup requests
  Future<void> _onBiometricSetupRequested(
    AuthBiometricSetupRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (_biometricService == null) {
      return; // Silently ignore if biometric service not available
    }

    try {
      await _biometricService.setBiometricEnabled(event.enable);
      // Don't change the current auth state, just update the setting
    } catch (e) {
      if (kDebugMode) {
        print('Biometric setup error: $e');
      }
      // Don't emit error for biometric setup, just log it
    }
  }
}
