import 'package:equatable/equatable.dart';

/// Base class for all authentication events
/// 
/// This abstract class defines the contract for authentication events
/// following the BLoC pattern and clean architecture principles.
abstract class AuthEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

/// Event to request email/password authentication
/// 
/// This event is triggered when the user attempts to log in
/// with email and password credentials.
class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  
  AuthLoginRequested({
    required this.email,
    required this.password,
  });
  
  @override
  List<Object?> get props => [email, password];
}

/// Event to request biometric authentication
/// 
/// This event is triggered when the user attempts to log in
/// using biometric authentication (fingerprint, face ID, etc.).
class AuthBiometricLoginRequested extends AuthEvent {
  final String email;
  final String reason;
  
  AuthBiometricLoginRequested({
    required this.email,
    this.reason = 'Authenticate to access your account',
  });
  
  @override
  List<Object?> get props => [email, reason];
}

/// Event to verify two-factor authentication code
/// 
/// This event is triggered when the user submits a two-factor
/// authentication code after initial login.
class AuthTwoFactorVerificationRequested extends AuthEvent {
  final String refCode;
  final String code;
  
  AuthTwoFactorVerificationRequested({
    required this.refCode,
    required this.code,
  });
  
  @override
  List<Object?> get props => [refCode, code];
}

/// Event to request logout from current session
/// 
/// This event is triggered when the user wants to log out
/// from the current session only.
class AuthLogoutRequested extends AuthEvent {}

/// Event to request global logout from all sessions
/// 
/// This event is triggered when the user wants to log out
/// from all active sessions across all devices.
class AuthGlobalLogoutRequested extends AuthEvent {}

/// Event to check current authentication status
/// 
/// This event is triggered when the app starts or when
/// authentication status needs to be verified.
class AuthStatusCheckRequested extends AuthEvent {}

/// Event to refresh authentication tokens
/// 
/// This event is triggered when tokens need to be refreshed
/// due to expiration or other reasons.
class AuthTokenRefreshRequested extends AuthEvent {}

/// Event to clear authentication error state
/// 
/// This event is triggered to clear error messages and
/// return to a neutral authentication state.
class AuthErrorCleared extends AuthEvent {}

/// Event to update user profile information
/// 
/// This event is triggered when user profile information
/// needs to be updated in the authentication state.
class AuthUserProfileUpdateRequested extends AuthEvent {}

/// Event to handle authentication session timeout
/// 
/// This event is triggered when the authentication session
/// expires and the user needs to re-authenticate.
class AuthSessionTimeoutOccurred extends AuthEvent {
  final String? message;
  
  AuthSessionTimeoutOccurred({this.message});
  
  @override
  List<Object?> get props => [message];
}

/// Event to handle biometric authentication setup
/// 
/// This event is triggered when the user wants to enable
/// or disable biometric authentication.
class AuthBiometricSetupRequested extends AuthEvent {
  final bool enable;
  final String email;
  final String password;
  
  AuthBiometricSetupRequested({
    required this.enable,
    required this.email,
    required this.password,
  });
  
  @override
  List<Object?> get props => [enable, email, password];
}
