import 'package:equatable/equatable.dart';
import '../../domain/entities/auth_user.dart';

/// Base class for all authentication states
/// 
/// This abstract class defines the contract for authentication states
/// following the BLoC pattern and clean architecture principles.
abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

/// Initial state when the authentication status is unknown
/// 
/// This state is used when the app starts and the authentication
/// status hasn't been determined yet.
class AuthInitial extends AuthState {}

/// State indicating that an authentication operation is in progress
/// 
/// This state is used during login, logout, token refresh, and other
/// authentication operations to show loading indicators.
class AuthLoading extends AuthState {
  final String? message;
  
  AuthLoading({this.message});
  
  @override
  List<Object?> get props => [message];
}

/// State indicating that the user is successfully authenticated
/// 
/// This state contains the authenticated user information and is used
/// when the user has valid authentication tokens.
class AuthAuthenticated extends AuthState {
  final AuthUser user;
  final String? accessToken;
  
  AuthAuthenticated({
    required this.user,
    this.accessToken,
  });
  
  @override
  List<Object?> get props => [user, accessToken];
}

/// State indicating that the user is not authenticated
/// 
/// This state is used when the user needs to log in or when
/// authentication tokens are invalid/expired.
class AuthUnauthenticated extends AuthState {
  final String? message;
  
  AuthUnauthenticated({this.message});
  
  @override
  List<Object?> get props => [message];
}

/// State indicating that two-factor authentication is required
/// 
/// This state is used when the user has provided valid credentials
/// but needs to complete two-factor authentication.
class AuthTwoFactorRequired extends AuthState {
  final String refCode;
  final String? message;
  
  AuthTwoFactorRequired({
    required this.refCode,
    this.message,
  });
  
  @override
  List<Object?> get props => [refCode, message];
}

/// State indicating that an authentication error has occurred
/// 
/// This state is used when authentication operations fail due to
/// various reasons like network errors, invalid credentials, etc.
class AuthError extends AuthState {
  final String message;
  final String? errorCode;
  
  AuthError({
    required this.message,
    this.errorCode,
  });
  
  @override
  List<Object?> get props => [message, errorCode];
}

/// State indicating that biometric authentication is in progress
/// 
/// This state is used specifically for biometric authentication
/// operations to provide appropriate UI feedback.
class AuthBiometricLoading extends AuthState {
  final String email;
  
  AuthBiometricLoading({required this.email});
  
  @override
  List<Object?> get props => [email];
}

/// State indicating that token refresh is in progress
/// 
/// This state is used when the authentication tokens are being
/// refreshed in the background.
class AuthTokenRefreshing extends AuthState {}

/// State indicating that logout is in progress
/// 
/// This state is used during logout operations to show appropriate
/// loading indicators.
class AuthLogoutLoading extends AuthState {
  final bool isGlobalLogout;
  
  AuthLogoutLoading({this.isGlobalLogout = false});
  
  @override
  List<Object?> get props => [isGlobalLogout];
}
