import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';

// Domain
import '../../domain/repositories/auth_repository.dart';
import '../../domain/services/biometric_service.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/verify_two_factor_usecase.dart';

// Data
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/datasources/auth_local_datasource.dart';
import '../../data/services/biometric_service_impl.dart';

// Core Services
import '../services/token_service.dart';
import '../services/session_service.dart';
import '../services/secure_storage_service.dart';

// BLoCs
import '../../bloc/auth/auth_bloc.dart';

final sl = GetIt.instance;

/// Initializes all dependencies for the application
///
/// This function sets up the dependency injection container following
/// clean architecture principles with proper dependency ordering:
/// 1. External dependencies (Flutter packages)
/// 2. Core services
/// 3. Data sources
/// 4. Repositories
/// 5. Use cases
/// 6. BLoCs
Future<void> init() async {
  //! External dependencies
  sl.registerLazySingleton(() => const FlutterSecureStorage());
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => LocalAuthentication());

  //! Core services
  sl.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(sl()),
  );

  sl.registerLazySingleton<TokenService>(
    () => TokenServiceImpl(sl()),
  );

  sl.registerLazySingleton<SessionService>(
    () => SessionServiceImpl(sl(), sl()),
  );
  sl.registerLazySingleton<BiometricService>(
    () => BiometricServiceImpl(sl(), sl()),
  );

  //! Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sl()),
  );

  //! Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl(), sl()),
  );

  //! Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => VerifyTwoFactorUseCase(sl()));

  //! BLoCs
  sl.registerFactory(() => AuthBloc(
        loginUseCase: sl(),
        logoutUseCase: sl(),
        verifyTwoFactorUseCase: sl(),
        authRepository: sl(),
        biometricService: sl(),
      ));
}

/// Resets all dependencies - useful for testing
Future<void> reset() async {
  await sl.reset();
}
