import 'package:equatable/equatable.dart';

/// Base class for all authentication failures
/// 
/// This abstract class defines the contract for authentication failures
/// following clean architecture principles and the failure pattern.
abstract class AuthFailure extends Equatable {
  final String message;
  
  const AuthFailure(this.message);
  
  @override
  List<Object?> get props => [message];
  
  @override
  String toString() => message;
}

/// Failure when network connectivity is unavailable
class NetworkFailure extends AuthFailure {
  const NetworkFailure(super.message);
}

/// Failure when server returns an error response
class ServerFailure extends AuthFailure {
  final int? statusCode;
  
  const ServerFailure(super.message, {this.statusCode});
  
  @override
  List<Object?> get props => [message, statusCode];
}

/// Failure when authentication credentials are invalid
class AuthenticationFailure extends AuthFailure {
  const AuthenticationFailure(super.message);
}

/// Failure when authentication token is invalid or expired
class TokenFailure extends AuthFailure {
  const TokenFailure(super.message);
}

/// Failure when two-factor authentication is required
class TwoFactorRequiredFailure extends AuthFailure {
  final String refCode;
  
  const TwoFactorRequiredFailure(super.message, this.refCode);
  
  @override
  List<Object?> get props => [message, refCode];
}

/// Failure when two-factor authentication code is invalid
class TwoFactorFailure extends AuthFailure {
  const TwoFactorFailure(super.message);
}

/// Failure when user account is not verified
class AccountNotVerifiedFailure extends AuthFailure {
  const AccountNotVerifiedFailure(super.message);
}

/// Failure when user account is locked or suspended
class AccountLockedFailure extends AuthFailure {
  const AccountLockedFailure(super.message);
}

/// Failure when session has expired
class SessionExpiredFailure extends AuthFailure {
  const SessionExpiredFailure(super.message);
}

/// Failure when local storage operations fail
class StorageFailure extends AuthFailure {
  const StorageFailure(super.message);
}

/// Failure when data parsing or validation fails
class DataFailure extends AuthFailure {
  const DataFailure(super.message);
}

/// Failure when an unexpected error occurs
class UnexpectedFailure extends AuthFailure {
  const UnexpectedFailure(super.message);
}

// Biometric-specific failures

/// Failure when biometric authentication is not available on the device
class BiometricNotAvailableFailure extends AuthFailure {
  const BiometricNotAvailableFailure(super.message);
}

/// Failure when no biometric credentials are enrolled on the device
class BiometricNotEnrolledFailure extends AuthFailure {
  const BiometricNotEnrolledFailure(super.message);
}

/// Failure when biometric authentication fails (wrong biometric)
class BiometricAuthenticationFailure extends AuthFailure {
  const BiometricAuthenticationFailure(super.message);
}

/// Failure when biometric authentication is locked due to too many attempts
class BiometricLockedFailure extends AuthFailure {
  const BiometricLockedFailure(super.message);
}

/// Failure when user cancels biometric authentication
class BiometricCancelledFailure extends AuthFailure {
  const BiometricCancelledFailure(super.message);
}

/// Failure when a system error occurs during biometric authentication
class BiometricSystemFailure extends AuthFailure {
  const BiometricSystemFailure(super.message);
}

// Session-specific failures

/// Failure when session operations fail
class SessionFailure extends AuthFailure {
  const SessionFailure(super.message);
}

/// Failure when session timeout occurs
class SessionTimeoutFailure extends AuthFailure {
  const SessionTimeoutFailure(super.message);
}

/// Failure when session validation fails
class SessionValidationFailure extends AuthFailure {
  const SessionValidationFailure(super.message);
}
