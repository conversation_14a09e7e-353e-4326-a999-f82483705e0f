import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Abstract service for secure storage operations
/// 
/// This service provides a clean interface for secure storage operations,
/// abstracting away the underlying FlutterSecureStorage implementation.
/// It follows the dependency inversion principle by providing an abstraction
/// that can be easily mocked for testing.
abstract class SecureStorageService {
  /// Writes a key-value pair to secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to store the value under
  /// - [value]: The value to store
  /// 
  /// Throws [SecureStorageException] if the operation fails
  Future<void> write(String key, String value);

  /// Reads a value from secure storage by key
  /// 
  /// Parameters:
  /// - [key]: The key to read the value for
  /// 
  /// Returns the stored value or null if the key doesn't exist
  /// 
  /// Throws [SecureStorageException] if the operation fails
  Future<String?> read(String key);

  /// Deletes a key-value pair from secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to delete
  /// 
  /// Throws [SecureStorageException] if the operation fails
  Future<void> delete(String key);

  /// Deletes all key-value pairs from secure storage
  /// 
  /// Throws [SecureStorageException] if the operation fails
  Future<void> deleteAll();

  /// Checks if a key exists in secure storage
  /// 
  /// Parameters:
  /// - [key]: The key to check for existence
  /// 
  /// Returns true if the key exists, false otherwise
  Future<bool> containsKey(String key);

  /// Gets all keys from secure storage
  /// 
  /// Returns a set of all keys in secure storage
  Future<Set<String>> getAllKeys();
}

/// Implementation of SecureStorageService using FlutterSecureStorage
class SecureStorageServiceImpl implements SecureStorageService {
  final FlutterSecureStorage _secureStorage;

  const SecureStorageServiceImpl(this._secureStorage);

  @override
  Future<void> write(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      throw SecureStorageException('Failed to write to secure storage: $e');
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      throw SecureStorageException('Failed to read from secure storage: $e');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      throw SecureStorageException('Failed to delete from secure storage: $e');
    }
  }

  @override
  Future<void> deleteAll() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      throw SecureStorageException('Failed to delete all from secure storage: $e');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return await _secureStorage.containsKey(key: key);
    } catch (e) {
      throw SecureStorageException('Failed to check key in secure storage: $e');
    }
  }

  @override
  Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      throw SecureStorageException('Failed to get all keys from secure storage: $e');
    }
  }
}

/// Exception thrown when secure storage operations fail
class SecureStorageException implements Exception {
  final String message;
  
  const SecureStorageException(this.message);
  
  @override
  String toString() => 'SecureStorageException: $message';
}
