import 'package:flutter/foundation.dart';
import 'secure_storage_service.dart';
import 'token_service.dart';

/// Abstract service for session management operations
/// 
/// This service handles user session lifecycle including start, refresh,
/// end, and validation following clean architecture principles.
abstract class SessionService {
  /// Starts a new user session
  /// 
  /// Parameters:
  /// - [accessToken]: The access token for the session
  /// - [refreshToken]: The refresh token for the session
  /// - [email]: The user's email address
  /// - [password]: The user's password (for biometric storage)
  /// 
  /// Throws [SessionException] if session start fails
  Future<void> startSession({
    required String accessToken,
    required String refreshToken,
    required String email,
    required String password,
  });

  /// Refreshes the current session
  /// 
  /// Updates session tokens and extends session validity
  /// 
  /// Throws [SessionException] if session refresh fails
  Future<void> refreshSession();

  /// Ends the current session
  /// 
  /// Clears session data while preserving user preferences
  /// 
  /// Parameters:
  /// - [preserveBiometric]: Whether to preserve biometric credentials
  /// - [preserveTheme]: Whether to preserve theme preferences
  /// 
  /// Throws [SessionException] if session end fails
  Future<void> endSession({
    bool preserveBiometric = true,
    bool preserveTheme = true,
  });

  /// Validates the current session
  /// 
  /// Checks if the session is valid and active
  /// 
  /// Returns true if session is valid, false otherwise
  Future<bool> isSessionValid();

  /// Gets the current session user email
  /// 
  /// Returns the email of the currently logged in user or null if no session
  Future<String?> getCurrentUserEmail();

  /// Checks if a user session exists
  /// 
  /// Returns true if any session data exists, false otherwise
  Future<bool> hasActiveSession();

  /// Sets two-factor authentication status for the session
  /// 
  /// Parameters:
  /// - [enabled]: Whether 2FA is enabled
  /// 
  /// Throws [SessionException] if setting 2FA status fails
  Future<void> setTwoFactorEnabled(bool enabled);

  /// Gets two-factor authentication status for the session
  /// 
  /// Returns true if 2FA is enabled, false otherwise
  Future<bool> isTwoFactorEnabled();
}

/// Implementation of SessionService
class SessionServiceImpl implements SessionService {
  final SecureStorageService _secureStorage;
  final TokenService _tokenService;

  static const String _emailKey = 'email';
  static const String _passwordKey = 'password';
  static const String _twoFactorKey = 'two_factor';
  static const String _biometricKey = 'biometric';
  static const String _themeModeKey = 'themeMode';

  const SessionServiceImpl(this._secureStorage, this._tokenService);

  @override
  Future<void> startSession({
    required String accessToken,
    required String refreshToken,
    required String email,
    required String password,
  }) async {
    try {
      // Store tokens
      await _tokenService.storeTokens(accessToken, refreshToken);
      
      // Store user credentials
      await _secureStorage.write(_emailKey, email);
      await _secureStorage.write(_passwordKey, password);
      
      // Initialize 2FA as disabled for new sessions
      await setTwoFactorEnabled(false);
      
      if (kDebugMode) {
        print("Session started for user: $email");
      }
    } catch (e) {
      throw SessionException('Failed to start session: $e');
    }
  }

  @override
  Future<void> refreshSession() async {
    try {
      // Token refresh is handled by TokenService
      await _tokenService.getAccessToken();
      
      if (kDebugMode) {
        print("Session refreshed");
      }
    } catch (e) {
      throw SessionException('Failed to refresh session: $e');
    }
  }

  @override
  Future<void> endSession({
    bool preserveBiometric = true,
    bool preserveTheme = true,
  }) async {
    try {
      // Get data to preserve before clearing
      String? biometricEnabled;
      String? email;
      String? password;
      String? themeMode;

      if (preserveBiometric) {
        biometricEnabled = await _secureStorage.read(_biometricKey);
        if (biometricEnabled == 'true') {
          email = await _secureStorage.read(_emailKey);
          password = await _secureStorage.read(_passwordKey);
        }
      }

      if (preserveTheme) {
        themeMode = await _secureStorage.read(_themeModeKey);
      }

      // Clear all session data
      await _secureStorage.deleteAll();
      await _tokenService.clearTokens();

      // Restore preserved data
      if (preserveBiometric && biometricEnabled == 'true' && email != null && password != null) {
        await _secureStorage.write(_emailKey, email);
        await _secureStorage.write(_passwordKey, password);
        await _secureStorage.write(_biometricKey, 'true');
      }

      if (preserveTheme && themeMode != null) {
        await _secureStorage.write(_themeModeKey, themeMode);
      }

      if (kDebugMode) {
        print("Session ended");
      }
    } catch (e) {
      throw SessionException('Failed to end session: $e');
    }
  }

  @override
  Future<bool> isSessionValid() async {
    try {
      return await _tokenService.isTokenValid();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> getCurrentUserEmail() async {
    try {
      return await _secureStorage.read(_emailKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> hasActiveSession() async {
    try {
      final email = await _secureStorage.read(_emailKey);
      final accessToken = await _tokenService.getAccessToken();
      return email != null && accessToken != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setTwoFactorEnabled(bool enabled) async {
    try {
      await _secureStorage.write(_twoFactorKey, enabled.toString());
    } catch (e) {
      throw SessionException('Failed to set two-factor status: $e');
    }
  }

  @override
  Future<bool> isTwoFactorEnabled() async {
    try {
      final twoFactorEnabled = await _secureStorage.read(_twoFactorKey);
      return twoFactorEnabled == 'true';
    } catch (e) {
      return false;
    }
  }
}

/// Exception thrown when session operations fail
class SessionException implements Exception {
  final String message;
  
  const SessionException(this.message);
  
  @override
  String toString() => 'SessionException: $message';
}
