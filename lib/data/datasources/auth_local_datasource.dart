import '../../core/services/secure_storage_service.dart';
import '../models/auth_user_model.dart';

/// Abstract interface for authentication local data source
/// 
/// This interface defines the contract for local authentication data operations
/// following clean architecture principles.
abstract class AuthLocalDataSource {
  /// Stores authentication tokens securely
  /// 
  /// Parameters:
  /// - [accessToken]: The access token to store
  /// - [refreshToken]: The refresh token to store
  /// 
  /// Throws [LocalDataException] if storage fails
  Future<void> storeTokens(String accessToken, String refreshToken);

  /// Gets the stored access token
  /// 
  /// Returns the access token or null if not found
  Future<String?> getAccessToken();

  /// Gets the stored refresh token
  /// 
  /// Returns the refresh token or null if not found
  Future<String?> getRefreshToken();

  /// Clears all stored authentication tokens
  /// 
  /// Throws [LocalDataException] if clearing fails
  Future<void> clearTokens();

  /// Stores user credentials for biometric authentication
  /// 
  /// Parameters:
  /// - [email]: User's email address
  /// - [password]: User's password
  /// 
  /// Throws [LocalDataException] if storage fails
  Future<void> storeCredentials(String email, String password);

  /// Gets stored user credentials
  /// 
  /// Returns a map with 'email' and 'password' keys, or null if not found
  Future<Map<String, String>?> getCredentials();

  /// Clears stored user credentials
  /// 
  /// Throws [LocalDataException] if clearing fails
  Future<void> clearCredentials();

  /// Stores user profile information
  /// 
  /// Parameters:
  /// - [user]: The user model to store
  /// 
  /// Throws [LocalDataException] if storage fails
  Future<void> storeUserProfile(AuthUserModel user);

  /// Gets stored user profile information
  /// 
  /// Returns the user model or null if not found
  Future<AuthUserModel?> getUserProfile();

  /// Clears stored user profile information
  /// 
  /// Throws [LocalDataException] if clearing fails
  Future<void> clearUserProfile();

  /// Sets biometric authentication preference
  /// 
  /// Parameters:
  /// - [enabled]: Whether biometric authentication is enabled
  /// 
  /// Throws [LocalDataException] if storage fails
  Future<void> setBiometricEnabled(bool enabled);

  /// Gets biometric authentication preference
  /// 
  /// Returns true if biometric authentication is enabled, false otherwise
  Future<bool> isBiometricEnabled();

  /// Sets two-factor authentication status
  /// 
  /// Parameters:
  /// - [enabled]: Whether two-factor authentication is enabled
  /// 
  /// Throws [LocalDataException] if storage fails
  Future<void> setTwoFactorEnabled(bool enabled);

  /// Gets two-factor authentication status
  /// 
  /// Returns true if two-factor authentication is enabled, false otherwise
  Future<bool> isTwoFactorEnabled();

  /// Clears all authentication data while preserving user preferences
  /// 
  /// Parameters:
  /// - [preserveBiometric]: Whether to preserve biometric credentials
  /// - [preserveTheme]: Whether to preserve theme preferences
  /// 
  /// Throws [LocalDataException] if clearing fails
  Future<void> clearAllData({
    bool preserveBiometric = true,
    bool preserveTheme = true,
  });
}

/// Implementation of AuthLocalDataSource using SecureStorageService
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SecureStorageService _secureStorage;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _emailKey = 'email';
  static const String _passwordKey = 'password';
  static const String _userProfileKey = 'user_profile';
  static const String _biometricKey = 'biometric';
  static const String _twoFactorKey = 'two_factor';
  static const String _themeModeKey = 'themeMode';

  const AuthLocalDataSourceImpl(this._secureStorage);

  @override
  Future<void> storeTokens(String accessToken, String refreshToken) async {
    try {
      await _secureStorage.write(_accessTokenKey, accessToken);
      await _secureStorage.write(_refreshTokenKey, refreshToken);
    } catch (e) {
      throw LocalDataException('Failed to store tokens: $e');
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.read(_accessTokenKey);
    } catch (e) {
      throw LocalDataException('Failed to get access token: $e');
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(_refreshTokenKey);
    } catch (e) {
      throw LocalDataException('Failed to get refresh token: $e');
    }
  }

  @override
  Future<void> clearTokens() async {
    try {
      await _secureStorage.delete(_accessTokenKey);
      await _secureStorage.delete(_refreshTokenKey);
    } catch (e) {
      throw LocalDataException('Failed to clear tokens: $e');
    }
  }

  @override
  Future<void> storeCredentials(String email, String password) async {
    try {
      await _secureStorage.write(_emailKey, email);
      await _secureStorage.write(_passwordKey, password);
    } catch (e) {
      throw LocalDataException('Failed to store credentials: $e');
    }
  }

  @override
  Future<Map<String, String>?> getCredentials() async {
    try {
      final email = await _secureStorage.read(_emailKey);
      final password = await _secureStorage.read(_passwordKey);
      
      if (email != null && password != null) {
        return {'email': email, 'password': password};
      }
      return null;
    } catch (e) {
      throw LocalDataException('Failed to get credentials: $e');
    }
  }

  @override
  Future<void> clearCredentials() async {
    try {
      await _secureStorage.delete(_emailKey);
      await _secureStorage.delete(_passwordKey);
    } catch (e) {
      throw LocalDataException('Failed to clear credentials: $e');
    }
  }

  @override
  Future<void> storeUserProfile(AuthUserModel user) async {
    try {
      final userJson = user.toJson();
      await _secureStorage.write(_userProfileKey, userJson.toString());
    } catch (e) {
      throw LocalDataException('Failed to store user profile: $e');
    }
  }

  @override
  Future<AuthUserModel?> getUserProfile() async {
    try {
      final userJsonString = await _secureStorage.read(_userProfileKey);
      if (userJsonString != null) {
        // Note: In a real implementation, you'd want to properly parse JSON
        // For now, we'll return null and rely on remote data source
        return null;
      }
      return null;
    } catch (e) {
      throw LocalDataException('Failed to get user profile: $e');
    }
  }

  @override
  Future<void> clearUserProfile() async {
    try {
      await _secureStorage.delete(_userProfileKey);
    } catch (e) {
      throw LocalDataException('Failed to clear user profile: $e');
    }
  }

  @override
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await _secureStorage.write(_biometricKey, enabled.toString());
    } catch (e) {
      throw LocalDataException('Failed to set biometric enabled: $e');
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      final biometricEnabled = await _secureStorage.read(_biometricKey);
      return biometricEnabled == 'true';
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setTwoFactorEnabled(bool enabled) async {
    try {
      await _secureStorage.write(_twoFactorKey, enabled.toString());
    } catch (e) {
      throw LocalDataException('Failed to set two-factor enabled: $e');
    }
  }

  @override
  Future<bool> isTwoFactorEnabled() async {
    try {
      final twoFactorEnabled = await _secureStorage.read(_twoFactorKey);
      return twoFactorEnabled == 'true';
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearAllData({
    bool preserveBiometric = true,
    bool preserveTheme = true,
  }) async {
    try {
      // Get data to preserve before clearing
      String? biometricEnabled;
      String? email;
      String? password;
      String? themeMode;

      if (preserveBiometric) {
        biometricEnabled = await _secureStorage.read(_biometricKey);
        if (biometricEnabled == 'true') {
          email = await _secureStorage.read(_emailKey);
          password = await _secureStorage.read(_passwordKey);
        }
      }

      if (preserveTheme) {
        themeMode = await _secureStorage.read(_themeModeKey);
      }

      // Clear all data
      await _secureStorage.deleteAll();

      // Restore preserved data
      if (preserveBiometric && biometricEnabled == 'true' && email != null && password != null) {
        await _secureStorage.write(_emailKey, email);
        await _secureStorage.write(_passwordKey, password);
        await _secureStorage.write(_biometricKey, 'true');
      }

      if (preserveTheme && themeMode != null) {
        await _secureStorage.write(_themeModeKey, themeMode);
      }
    } catch (e) {
      throw LocalDataException('Failed to clear all data: $e');
    }
  }
}

/// Exception thrown when local data operations fail
class LocalDataException implements Exception {
  final String message;
  
  const LocalDataException(this.message);
  
  @override
  String toString() => 'LocalDataException: $message';
}
