import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../utils/getDeviceID.dart';
import '../../view_model/custom_exception.dart';
import '../models/auth_result_model.dart';
import '../models/auth_user_model.dart';

/// Abstract interface for authentication remote data source
///
/// This interface defines the contract for remote authentication operations
/// following clean architecture principles.
abstract class AuthRemoteDataSource {
  /// Authenticates user with email and password
  ///
  /// Parameters:
  /// - [email]: User's email address
  /// - [password]: User's password (base64 encoded)
  ///
  /// Returns [AuthResultModel] with authentication result
  ///
  /// Throws [CustomException] for various error conditions
  Future<AuthResultModel> login(String email, String password);

  /// Verifies two-factor authentication code
  ///
  /// Parameters:
  /// - [refCode]: Reference code from initial login
  /// - [twoFactorCode]: Two-factor authentication code
  ///
  /// Returns [AuthResultModel] with authentication result
  ///
  /// Throws [CustomException] for various error conditions
  Future<AuthResultModel> verifyTwoFactor(String refCode, String twoFactorCode);

  /// Refreshes access token using refresh token
  ///
  /// Parameters:
  /// - [refreshToken]: Current refresh token
  ///
  /// Returns new access and refresh tokens
  ///
  /// Throws [CustomException] for various error conditions
  Future<Map<String, String>> refreshToken(String refreshToken);

  /// Gets current user profile information
  ///
  /// Parameters:
  /// - [accessToken]: Current access token
  ///
  /// Returns [AuthUserModel] with user information
  ///
  /// Throws [CustomException] for various error conditions
  Future<AuthUserModel> getUserProfile(String accessToken);

  /// Logs out user from current session
  ///
  /// Parameters:
  /// - [accessToken]: Current access token
  ///
  /// Throws [CustomException] for various error conditions
  Future<void> logout(String accessToken);

  /// Logs out user from all sessions globally
  ///
  /// Parameters:
  /// - [accessToken]: Current access token
  ///
  /// Throws [CustomException] for various error conditions
  Future<void> globalLogout(String accessToken);
}

/// Implementation of AuthRemoteDataSource
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final http.Client _client;

  static const String _au1Url = 'https://api.nudron.com/prod/au1';
  static const String _au3Url = 'https://api.nudron.com/prod/au3';

  const AuthRemoteDataSourceImpl(this._client);

  @override
  Future<AuthResultModel> login(String email, String password) async {
    // Convert password to base64
    final passwordBase64 = base64.encode(utf8.encode(password));
    final body = '02$email|$passwordBase64';

    try {
      final response = await _makeRequest(body, url: _au1Url);
      return AuthResultModel.fromApiResponse(response);
    } catch (e) {
      throw CustomException('Login failed: $e');
    }
  }

  @override
  Future<AuthResultModel> verifyTwoFactor(
      String refCode, String twoFactorCode) async {
    final body = '03$refCode|$twoFactorCode';

    try {
      final response = await _makeRequest(body, url: _au1Url);

      if (response == '0') {
        return AuthResultModel.failure('Incorrect code');
      } else if (response == '1') {
        return AuthResultModel.failure('Code expired');
      }

      final splitResponse = response.split('|');
      if (splitResponse.length == 2) {
        return AuthResultModel(
          success: true,
          accessToken: splitResponse[0],
          refreshToken: splitResponse[1],
        );
      }

      return AuthResultModel.failure('Unexpected response');
    } catch (e) {
      throw CustomException('Two-factor verification failed: $e');
    }
  }

  @override
  Future<Map<String, String>> refreshToken(String refreshToken) async {
    final body = '04$refreshToken';

    try {
      final response = await _makeRequest(body, url: _au1Url);

      if (response == '0') {
        throw CustomException(
            'Redirecting to login page.. Please login again.');
      }

      final splitResponse = response.split('|');
      if (splitResponse.length == 2) {
        return {
          'accessToken': splitResponse[0],
          'refreshToken': splitResponse[1],
        };
      }

      throw CustomException('Unexpected response');
    } catch (e) {
      throw CustomException('Token refresh failed: $e');
    }
  }

  @override
  Future<AuthUserModel> getUserProfile(String accessToken) async {
    const body = '07';

    try {
      final response =
          await _makeRequest(body, url: _au3Url, accessToken: accessToken);
      final json = jsonDecode(response);
      return AuthUserModel.fromJson(json);
    } catch (e) {
      throw CustomException('Failed to get user profile: $e');
    }
  }

  @override
  Future<void> logout(String accessToken) async {
    const body = '08';

    try {
      final response =
          await _makeRequest(body, url: _au3Url, accessToken: accessToken);

      if (response == '0') {
        throw CustomException('Error processing request');
      }
    } catch (e) {
      throw CustomException('Logout failed: $e');
    }
  }

  @override
  Future<void> globalLogout(String accessToken) async {
    const body = '09';

    try {
      final response =
          await _makeRequest(body, url: _au3Url, accessToken: accessToken);

      if (response == '0') {
        throw CustomException('Error processing request');
      }
    } catch (e) {
      throw CustomException('Global logout failed: $e');
    }
  }

  /// Makes HTTP request to the authentication API
  ///
  /// This method handles the common request logic including headers,
  /// connectivity checks, and error handling.
  Future<String> _makeRequest(
    String body, {
    String url = _au1Url,
    String? accessToken,
    Duration? timeout,
  }) async {
    // Check connectivity
    final connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      throw CustomException('No internet connection');
    }

    // Get user agent
    String userAgent = await DeviceInfoUtil.getUserAgent();

    // Prepare headers
    final headers = {
      'User-Agent': userAgent,
      'medium': 'phone',
      'Content-Type': 'text/plain',
      if (accessToken != null) 'Authorization': 'Bearer $accessToken',
      if (url == _au1Url) 'tenantID': "d14b3819-5e90-4b1e-8821-9fcb72684627",
      if (url == _au1Url) 'clientID': "WaterMeteringMobile2",
    };

    // Create request
    var request = http.Request('POST', Uri.parse(url));
    request.body = body;
    request.headers.addAll(headers);

    if (kDebugMode) {
      print("URL: ${request.url}");
      print("Body: ${request.body}");
      print("Headers: ${request.headers}");
    }

    try {
      // Send request
      http.StreamedResponse response = await _client
          .send(request)
          .timeout(timeout ?? const Duration(seconds: 5));

      if (kDebugMode) {
        print("Status Code: ${response.statusCode}");
      }

      if (response.statusCode == 200) {
        var responseBody = await response.stream.bytesToString();
        if (kDebugMode) {
          print("Response: $responseBody");
        }
        return responseBody;
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        throw CustomException('Redirecting to login page.. Please login again');
      } else {
        String responseBody = await response.stream.bytesToString();
        print("Error Response: $responseBody");
        throw CustomException(responseBody);
      }
    } on TimeoutException {
      throw CustomException('Request timed out');
    }
  }
}
