import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_user.dart';
import 'auth_user_model.dart';

/// Data model for AuthResult that extends the domain entity
///
/// This model provides JSON serialization capabilities while maintaining
/// compatibility with the domain entity.
class AuthResultModel extends AuthResult {
  const AuthResultModel({
    required super.success,
    super.user,
    super.accessToken,
    super.refreshToken,
    super.twoFactorRefCode,
    super.error,
    super.requiresTwoFactor = false,
  });

  /// Creates an AuthResultModel representing a successful authentication
  factory AuthResultModel.success({
    required AuthUser user,
    required String accessToken,
    required String refreshToken,
  }) {
    return AuthResultModel(
      success: true,
      user: user,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  /// Creates an AuthResultModel representing a two-factor authentication requirement
  factory AuthResultModel.twoFactorRequired(String refCode) {
    return AuthResultModel(
      success: false,
      twoFactorRefCode: refCode,
      requiresTwoFactor: true,
    );
  }

  /// Creates an AuthResultModel representing a failed authentication
  factory AuthResultModel.failure(String errorMessage) {
    return AuthResultModel(
      success: false,
      error: errorMessage,
    );
  }

  /// Creates an AuthResultModel from a JSON map
  ///
  /// This factory constructor handles different response formats from the API
  factory AuthResultModel.fromJson(Map<String, dynamic> json) {
    // Check if this is a user profile response (successful authentication)
    if (json.containsKey('userID') || json.containsKey('id')) {
      return AuthResultModel.success(
        user: AuthUserModel.fromJson(json),
        accessToken: '', // Will be set separately
        refreshToken: '', // Will be set separately
      );
    }

    // Check if this is an error response
    if (json.containsKey('error')) {
      return AuthResultModel.failure(json['error']);
    }

    // Default to failure if format is unknown
    return AuthResultModel.failure('Unknown response format');
  }

  /// Creates an AuthResultModel from API response string
  ///
  /// This factory handles the specific response format from the authentication API
  factory AuthResultModel.fromApiResponse(String response) {
    if (response == '0') {
      return AuthResultModel.failure('Incorrect email or password');
    } else if (response == '10' || response == '01' || response == '00') {
      return AuthResultModel.failure('Email or phone unverified');
    }

    final splitResponse = response.split('|');

    if (splitResponse.length == 2) {
      // Successful login with tokens
      return AuthResultModel(
        success: true,
        accessToken: splitResponse[0],
        refreshToken: splitResponse[1],
      );
    } else if (splitResponse.length == 1) {
      // Two-factor authentication required
      return AuthResultModel.twoFactorRequired(response);
    }

    return AuthResultModel.failure('Unexpected response');
  }

  /// Converts the AuthResultModel to a JSON map
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'success': success,
    };

    if (user != null) {
      json['user'] = (user as AuthUserModel).toJson();
    }

    if (accessToken != null) {
      json['accessToken'] = accessToken;
    }

    if (refreshToken != null) {
      json['refreshToken'] = refreshToken;
    }

    if (twoFactorRefCode != null) {
      json['twoFactorRefCode'] = twoFactorRefCode;
    }

    if (error != null) {
      json['error'] = error;
    }

    return json;
  }

  /// Creates an AuthResultModel from an AuthResult domain entity
  factory AuthResultModel.fromEntity(AuthResult result) {
    return AuthResultModel(
      success: result.success,
      user: result.user,
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      twoFactorRefCode: result.twoFactorRefCode,
      error: result.error,
      requiresTwoFactor: result.requiresTwoFactor,
    );
  }

  /// Creates a copy of this model with updated fields
  AuthResultModel copyWith({
    bool? success,
    AuthUser? user,
    String? accessToken,
    String? refreshToken,
    String? twoFactorRefCode,
    String? error,
    bool? requiresTwoFactor,
  }) {
    return AuthResultModel(
      success: success ?? this.success,
      user: user ?? this.user,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      twoFactorRefCode: twoFactorRefCode ?? this.twoFactorRefCode,
      error: error ?? this.error,
      requiresTwoFactor: requiresTwoFactor ?? this.requiresTwoFactor,
    );
  }

  @override
  String toString() {
    return 'AuthResultModel(success: $success, user: $user, '
        'hasTokens: ${accessToken != null && refreshToken != null}, '
        'twoFactorRequired: ${twoFactorRefCode != null}, '
        'error: $error)';
  }
}
