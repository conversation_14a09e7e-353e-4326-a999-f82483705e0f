import '../../domain/entities/auth_user.dart';

/// Data model for AuthUser that extends the domain entity
/// 
/// This model provides JSON serialization capabilities while maintaining
/// compatibility with the domain entity. It follows the clean architecture
/// principle of data models extending domain entities.
class AuthUserModel extends AuthUser {
  const AuthUserModel({
    required super.id,
    required super.name,
    required super.email,
    required super.emailVerified,
    required super.phone,
    required super.phoneVerified,
    super.lastPassChange,
    super.lastUpdate,
    super.multiFactor,
  });

  /// Creates an AuthUserModel from a JSON map
  /// 
  /// This factory constructor handles the API response format and maps
  /// the fields to the appropriate domain entity properties.
  factory AuthUserModel.fromJson(Map<String, dynamic> json) {
    return AuthUserModel(
      id: json['userID'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      emailVerified: json['emailVerified'] ?? false,
      phone: json['phone'] ?? '',
      phoneVerified: json['phoneVerified'] ?? false,
      lastPassChange: json['lastPassChange'],
      lastUpdate: json['lastUpdate'],
      multiFactor: json['multiFactor'],
    );
  }

  /// Converts the AuthUserModel to a JSON map
  /// 
  /// This method serializes the model for storage or API requests.
  Map<String, dynamic> toJson() {
    return {
      'userID': id,
      'name': name,
      'email': email,
      'emailVerified': emailVerified,
      'phone': phone,
      'phoneVerified': phoneVerified,
      'lastPassChange': lastPassChange,
      'lastUpdate': lastUpdate,
      'multiFactor': multiFactor,
    };
  }

  /// Creates an AuthUserModel from an AuthUser domain entity
  /// 
  /// This factory constructor allows conversion from domain entities
  /// to data models when needed.
  factory AuthUserModel.fromEntity(AuthUser user) {
    return AuthUserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      phone: user.phone,
      phoneVerified: user.phoneVerified,
      lastPassChange: user.lastPassChange,
      lastUpdate: user.lastUpdate,
      multiFactor: user.multiFactor,
    );
  }

  /// Creates a copy of this model with updated fields
  /// 
  /// This method allows for immutable updates to the model.
  AuthUserModel copyWith({
    String? id,
    String? name,
    String? email,
    bool? emailVerified,
    String? phone,
    bool? phoneVerified,
    String? lastPassChange,
    int? lastUpdate,
    String? multiFactor,
  }) {
    return AuthUserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      phone: phone ?? this.phone,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      lastPassChange: lastPassChange ?? this.lastPassChange,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      multiFactor: multiFactor ?? this.multiFactor,
    );
  }

  @override
  String toString() {
    return 'AuthUserModel(id: $id, name: $name, email: $email, emailVerified: $emailVerified, '
        'phone: $phone, phoneVerified: $phoneVerified, hasTwoFactor: $hasTwoFactorEnabled)';
  }
}
