import 'package:flutter/foundation.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;

import '../../core/error/auth_failures.dart';
import '../../core/services/secure_storage_service.dart';
import '../../domain/services/biometric_service.dart';

/// Implementation of BiometricService using local_auth package
///
/// This service provides biometric authentication functionality following
/// clean architecture principles and handles platform-specific biometric operations.
class BiometricServiceImpl implements BiometricService {
  final LocalAuthentication _localAuth;
  final SecureStorageService _secureStorage;

  static const String _biometricEnabledKey = 'biometric_enabled';

  const BiometricServiceImpl(this._localAuth, this._secureStorage);

  @override
  Future<bool> isAvailable() async {
    try {
      // Check if device supports biometric authentication
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      if (!isDeviceSupported) return false;

      // Check if biometric authentication is available
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return canCheckBiometrics;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking biometric availability: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> isEnrolled() async {
    try {
      // First check if biometric authentication is available
      final isAvailableResult = await isAvailable();
      if (!isAvailableResult) return false;

      // Get available biometric types
      final availableBiometrics = await _localAuth.getAvailableBiometrics();

      // Check if any biometric types are enrolled
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking biometric enrollment: $e');
      }
      return false;
    }
  }

  @override
  Future<BiometricAuthResult> authenticate({
    required String reason,
  }) async {
    try {
      // Check if biometric authentication is available
      final isAvailableResult = await isAvailable();
      if (!isAvailableResult) {
        return BiometricAuthResult.failure(
          BiometricNotAvailableFailure(
              'Biometric authentication not available'),
        );
      }

      // Check if biometric authentication is enrolled
      final isEnrolledResult = await isEnrolled();
      if (!isEnrolledResult) {
        return BiometricAuthResult.failure(
          BiometricNotEnrolledFailure('No biometric credentials enrolled'),
        );
      }

      // Perform biometric authentication
      final didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.failure(
          BiometricAuthenticationFailure('Biometric authentication failed'),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Biometric authentication error: $e');
      }

      // Map platform-specific errors to domain failures
      if (e.toString().contains(auth_error.notAvailable)) {
        return BiometricAuthResult.failure(
          BiometricNotAvailableFailure(
              'Biometric authentication not available'),
        );
      } else if (e.toString().contains(auth_error.notEnrolled)) {
        return BiometricAuthResult.failure(
          BiometricNotEnrolledFailure('No biometric credentials enrolled'),
        );
      } else if (e.toString().contains(auth_error.lockedOut) ||
          e.toString().contains(auth_error.permanentlyLockedOut)) {
        return BiometricAuthResult.failure(
          BiometricLockedFailure('Biometric authentication locked'),
        );
      } else if (e.toString().contains('UserCancel') ||
          e.toString().contains('user_cancel')) {
        return BiometricAuthResult.failure(
          BiometricCancelledFailure('User cancelled biometric authentication'),
        );
      } else {
        return BiometricAuthResult.failure(
          BiometricSystemFailure(
              'System error during biometric authentication: $e'),
        );
      }
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      final enabled = await _secureStorage.read(_biometricEnabledKey);
      return enabled == 'true';
    } catch (e) {
      if (kDebugMode) {
        print('Error checking biometric enabled status: $e');
      }
      return false;
    }
  }

  @override
  Future<BiometricSettingsResult> setBiometricEnabled(bool enabled) async {
    try {
      await _secureStorage.write(_biometricEnabledKey, enabled.toString());
      return BiometricSettingsResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('Error setting biometric enabled status: $e');
      }
      return BiometricSettingsResult.failure(
        BiometricSystemFailure('Failed to save biometric setting: $e'),
      );
    }
  }

  @override
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      // Check if biometric authentication is available
      final isAvailableResult = await isAvailable();
      if (!isAvailableResult) return [];

      // Get available biometric types
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available biometrics: $e');
      }
      return [];
    }
  }

  @override
  Future<bool> canUseBiometric() async {
    try {
      // Check if biometric authentication is available and enrolled
      final isAvailableResult = await isAvailable();
      if (!isAvailableResult) return false;

      final isEnrolledResult = await isEnrolled();
      return isEnrolledResult;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if can use biometric: $e');
      }
      return false;
    }
  }
}
