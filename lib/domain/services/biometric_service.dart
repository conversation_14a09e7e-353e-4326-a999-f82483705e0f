import 'package:local_auth/local_auth.dart';
import '../../core/error/auth_failures.dart';

/// Domain service interface for biometric authentication operations
abstract class BiometricService {
  Future<bool> isAvailable();
  Future<bool> isEnrolled();
  Future<BiometricAuthResult> authenticate({required String reason});
  Future<List<BiometricType>> getAvailableBiometrics();
  Future<bool> isBiometricEnabled();
  Future<BiometricSettingsResult> setBiometricEnabled(bool enabled);
  Future<bool> canUseBiometric();
}

/// Result of a biometric authentication operation
class BiometricAuthResult {
  final bool success;
  final AuthFailure? failure;

  const BiometricAuthResult._({required this.success, this.failure});

  factory BiometricAuthResult.success() {
    return const BiometricAuthResult._(success: true);
  }

  factory BiometricAuthResult.failure(AuthFailure failure) {
    return BiometricAuthResult._(success: false, failure: failure);
  }
}

/// Result of a biometric settings operation
class BiometricSettingsResult {
  final bool success;
  final AuthFailure? failure;

  const BiometricSettingsResult._({required this.success, this.failure});

  factory BiometricSettingsResult.success() {
    return const BiometricSettingsResult._(success: true);
  }

  factory BiometricSettingsResult.failure(AuthFailure failure) {
    return BiometricSettingsResult._(success: false, failure: failure);
  }
}
