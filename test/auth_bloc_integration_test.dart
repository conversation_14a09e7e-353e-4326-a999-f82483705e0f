import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:water_metering/bloc/auth/auth_bloc.dart';
import 'package:water_metering/bloc/auth/auth_event.dart';
import 'package:water_metering/bloc/auth/auth_state.dart';
import 'package:water_metering/domain/usecases/login_usecase.dart';
import 'package:water_metering/domain/usecases/logout_usecase.dart';
import 'package:water_metering/domain/usecases/verify_two_factor_usecase.dart';
import 'package:water_metering/domain/repositories/auth_repository.dart';
import 'package:water_metering/domain/entities/auth_user.dart';
import 'package:water_metering/domain/entities/auth_result.dart';

// Generate mocks
@GenerateMocks(
    [LoginUseCase, LogoutUseCase, VerifyTwoFactorUseCase, AuthRepository])
import 'auth_bloc_integration_test.mocks.dart';

void main() {
  group('AuthBloc Integration Tests', () {
    late AuthBloc authBloc;
    late MockLoginUseCase mockLoginUseCase;
    late MockLogoutUseCase mockLogoutUseCase;
    late MockVerifyTwoFactorUseCase mockVerifyTwoFactorUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockLoginUseCase = MockLoginUseCase();
      mockLogoutUseCase = MockLogoutUseCase();
      mockVerifyTwoFactorUseCase = MockVerifyTwoFactorUseCase();
      mockAuthRepository = MockAuthRepository();
      authBloc = AuthBloc(
        loginUseCase: mockLoginUseCase,
        logoutUseCase: mockLogoutUseCase,
        verifyTwoFactorUseCase: mockVerifyTwoFactorUseCase,
        authRepository: mockAuthRepository,
      );
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state should be AuthInitial', () {
      expect(authBloc.state, equals(AuthInitial()));
    });

    test(
        'should emit [AuthLoading, AuthAuthenticated] when login is successful',
        () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      final user = AuthUser(
        id: '1',
        email: email,
        name: 'Test User',
        emailVerified: true,
        phone: '+1234567890',
        phoneVerified: true,
      );
      final authResult = AuthResult.success(user: user);

      when(mockLoginUseCase.call(email, password))
          .thenAnswer((_) async => authResult);

      // Act & Assert
      expectLater(
        authBloc.stream,
        emitsInOrder([
          AuthLoading(message: 'Signing in...'),
          AuthAuthenticated(user: user),
        ]),
      );

      authBloc.add(AuthLoginRequested(email: email, password: password));
    });

    test('should emit [AuthLoading, AuthError] when login fails', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'wrongpassword';
      final authResult = AuthResult.failure(error: 'Invalid credentials');

      when(mockLoginUseCase.call(email, password))
          .thenAnswer((_) async => authResult);

      // Act & Assert
      expectLater(
        authBloc.stream,
        emitsInOrder([
          AuthLoading(message: 'Signing in...'),
          AuthError(message: 'Invalid credentials'),
        ]),
      );

      authBloc.add(AuthLoginRequested(email: email, password: password));
    });

    test(
        'should emit [AuthLoading, AuthTwoFactorRequired] when 2FA is required',
        () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const refCode = 'REF123';
      final authResult =
          AuthResult.twoFactorRequired(twoFactorRefCode: refCode);

      when(mockLoginUseCase.call(email, password))
          .thenAnswer((_) async => authResult);

      // Act & Assert
      expectLater(
        authBloc.stream,
        emitsInOrder([
          AuthLoading(message: 'Signing in...'),
          AuthTwoFactorRequired(
            refCode: refCode,
            message: 'Please enter the code sent to your authenticator app/SMS',
          ),
        ]),
      );

      authBloc.add(AuthLoginRequested(email: email, password: password));
    });
  });
}
