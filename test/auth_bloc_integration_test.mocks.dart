// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in water_metering/test/auth_bloc_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;
import 'package:water_metering/domain/entities/auth_result.dart' as _i2;
import 'package:water_metering/domain/entities/auth_user.dart' as _i9;
import 'package:water_metering/domain/repositories/auth_repository.dart' as _i7;
import 'package:water_metering/domain/usecases/login_usecase.dart' as _i3;
import 'package:water_metering/domain/usecases/logout_usecase.dart' as _i5;
import 'package:water_metering/domain/usecases/verify_two_factor_usecase.dart'
    as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthResult_0 extends _i1.SmartFake implements _i2.AuthResult {
  _FakeAuthResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LoginUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginUseCase extends _i1.Mock implements _i3.LoginUseCase {
  MockLoginUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.AuthResult> call(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [
            email,
            password,
          ],
        ),
        returnValue: _i4.Future<_i2.AuthResult>.value(_FakeAuthResult_0(
          this,
          Invocation.method(
            #call,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i4.Future<_i2.AuthResult>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i5.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> call() => (super.noSuchMethod(
        Invocation.method(
          #call,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> callGlobal() => (super.noSuchMethod(
        Invocation.method(
          #callGlobal,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [VerifyTwoFactorUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockVerifyTwoFactorUseCase extends _i1.Mock
    implements _i6.VerifyTwoFactorUseCase {
  MockVerifyTwoFactorUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.AuthResult> call(
    String? refCode,
    String? code,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [
            refCode,
            code,
          ],
        ),
        returnValue: _i4.Future<_i2.AuthResult>.value(_FakeAuthResult_0(
          this,
          Invocation.method(
            #call,
            [
              refCode,
              code,
            ],
          ),
        )),
      ) as _i4.Future<_i2.AuthResult>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i7.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.AuthResult> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            email,
            password,
          ],
        ),
        returnValue: _i4.Future<_i2.AuthResult>.value(_FakeAuthResult_0(
          this,
          Invocation.method(
            #login,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i4.Future<_i2.AuthResult>);

  @override
  _i4.Future<_i2.AuthResult> loginWithBiometric(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithBiometric,
          [email],
        ),
        returnValue: _i4.Future<_i2.AuthResult>.value(_FakeAuthResult_0(
          this,
          Invocation.method(
            #loginWithBiometric,
            [email],
          ),
        )),
      ) as _i4.Future<_i2.AuthResult>);

  @override
  _i4.Future<void> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> globalLogout() => (super.noSuchMethod(
        Invocation.method(
          #globalLogout,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.AuthResult> verifyTwoFactor(
    String? refCode,
    String? code,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyTwoFactor,
          [
            refCode,
            code,
          ],
        ),
        returnValue: _i4.Future<_i2.AuthResult>.value(_FakeAuthResult_0(
          this,
          Invocation.method(
            #verifyTwoFactor,
            [
              refCode,
              code,
            ],
          ),
        )),
      ) as _i4.Future<_i2.AuthResult>);

  @override
  _i4.Future<String> refreshToken() => (super.noSuchMethod(
        Invocation.method(
          #refreshToken,
          [],
        ),
        returnValue: _i4.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #refreshToken,
            [],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<bool> isAuthenticated() => (super.noSuchMethod(
        Invocation.method(
          #isAuthenticated,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i9.AuthUser?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i4.Future<_i9.AuthUser?>.value(),
      ) as _i4.Future<_i9.AuthUser?>);
}
