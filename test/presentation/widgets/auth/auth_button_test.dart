import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:water_metering/presentation/widgets/auth/auth_button.dart';

void main() {
  group('AuthButton Widget Tests', () {
    Widget createTestWidget({
      required String text,
      VoidCallback? onPressed,
      bool isLoading = false,
      bool isDisabled = false,
      AuthButtonStyle style = AuthButtonStyle.primary,
      double? width,
      bool dynamicWidth = false,
      double? fontSize,
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) => MaterialApp(
          home: Scaffold(
            body: Center(
              child: AuthButton(
                text: text,
                onPressed: onPressed,
                isLoading: isLoading,
                isDisabled: isDisabled,
                style: style,
                width: width,
                dynamicWidth: dynamicWidth,
                fontSize: fontSize,
              ),
            ),
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render button with text', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
        ));

        expect(find.text('Login'), findsOneWidget);
        expect(find.byType(AuthButton), findsOneWidget);
      });

      testWidgets('should render primary button style by default',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
        ));

        final authButton = tester.widget<AuthButton>(find.byType(AuthButton));
        expect(authButton.style, equals(AuthButtonStyle.primary));
      });

      testWidgets('should render secondary button style when specified',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Cancel',
          onPressed: () {},
          style: AuthButtonStyle.secondary,
        ));

        final authButton = tester.widget<AuthButton>(find.byType(AuthButton));
        expect(authButton.style, equals(AuthButtonStyle.secondary));
      });

      testWidgets('should show loading indicator when isLoading is true',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
          isLoading: true,
        ));

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Login'), findsNothing);
      });

      testWidgets('should not show loading indicator when isLoading is false',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
          isLoading: false,
        ));

        expect(find.byType(CircularProgressIndicator), findsNothing);
        expect(find.text('Login'), findsOneWidget);
      });
    });

    group('Button States', () {
      testWidgets(
          'should be enabled when onPressed is provided and not disabled',
          (tester) async {
        bool buttonPressed = false;

        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {
            buttonPressed = true;
          },
          isDisabled: false,
        ));

        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        expect(buttonPressed, isTrue);
      });

      testWidgets('should be disabled when isDisabled is true', (tester) async {
        bool buttonPressed = false;

        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {
            buttonPressed = true;
          },
          isDisabled: true,
        ));

        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        expect(buttonPressed, isFalse);
      });

      testWidgets('should be disabled when isLoading is true', (tester) async {
        bool buttonPressed = false;

        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {
            buttonPressed = true;
          },
          isLoading: true,
        ));

        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        expect(buttonPressed, isFalse);
      });

      testWidgets('should be disabled when onPressed is null', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: null,
        ));

        // Should not throw when tapping
        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        // Test passes if no exception is thrown
      });
    });

    group('Button Styles', () {
      testWidgets(
          'should apply different colors for primary and secondary styles',
          (tester) async {
        // Test primary button
        await tester.pumpWidget(createTestWidget(
          text: 'Primary',
          onPressed: () {},
          style: AuthButtonStyle.primary,
        ));

        final primaryMaterial = tester.widget<Material>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(Material),
          ),
        );
        final primaryColor = primaryMaterial.color;

        // Test secondary button
        await tester.pumpWidget(createTestWidget(
          text: 'Secondary',
          onPressed: () {},
          style: AuthButtonStyle.secondary,
        ));

        final secondaryMaterial = tester.widget<Material>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(Material),
          ),
        );
        final secondaryColor = secondaryMaterial.color;

        // Colors should be different
        expect(primaryColor, isNot(equals(secondaryColor)));
      });

      testWidgets('should apply disabled color when button is disabled',
          (tester) async {
        // Test enabled button color
        await tester.pumpWidget(createTestWidget(
          text: 'Enabled',
          onPressed: () {},
          isDisabled: false,
        ));

        final enabledMaterial = tester.widget<Material>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(Material),
          ),
        );
        final enabledColor = enabledMaterial.color;

        // Test disabled button color
        await tester.pumpWidget(createTestWidget(
          text: 'Disabled',
          onPressed: () {},
          isDisabled: true,
        ));

        final disabledMaterial = tester.widget<Material>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(Material),
          ),
        );
        final disabledColor = disabledMaterial.color;

        // Colors should be different
        expect(enabledColor, isNot(equals(disabledColor)));
      });
    });

    group('Button Sizing', () {
      testWidgets('should use default width when not specified',
          (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
        ));

        final sizedBox = tester.widget<SizedBox>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(SizedBox),
          ),
        );

        expect(sizedBox.width, equals(112.w));
      });

      testWidgets('should use custom width when specified', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
          width: 200,
        ));

        final sizedBox = tester.widget<SizedBox>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(SizedBox),
          ),
        );

        expect(sizedBox.width, equals(200));
      });

      testWidgets('should use dynamic width when enabled', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Very Long Button Text',
          onPressed: () {},
          dynamicWidth: true,
        ));

        final sizedBox = tester.widget<SizedBox>(
          find.descendant(
            of: find.byType(AuthButton),
            matching: find.byType(SizedBox),
          ),
        );

        expect(sizedBox.width, isNull);
      });
    });

    group('Named Constructors', () {
      testWidgets('should create primary button with AuthButton.primary',
          (tester) async {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(375, 812),
            builder: (context, child) => MaterialApp(
              home: Scaffold(
                body: Center(
                  child: AuthButton.primary(
                    text: 'Primary Button',
                    onPressed: () {},
                  ),
                ),
              ),
            ),
          ),
        );

        final authButton = tester.widget<AuthButton>(find.byType(AuthButton));
        expect(authButton.style, equals(AuthButtonStyle.primary));
        expect(find.text('Primary Button'), findsOneWidget);
      });

      testWidgets('should create secondary button with AuthButton.secondary',
          (tester) async {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(375, 812),
            builder: (context, child) => MaterialApp(
              home: Scaffold(
                body: Center(
                  child: AuthButton.secondary(
                    text: 'Secondary Button',
                    onPressed: () {},
                  ),
                ),
              ),
            ),
          ),
        );

        final authButton = tester.widget<AuthButton>(find.byType(AuthButton));
        expect(authButton.style, equals(AuthButtonStyle.secondary));
        expect(find.text('Secondary Button'), findsOneWidget);
      });
    });

    group('Interaction Tests', () {
      testWidgets('should handle multiple taps correctly', (tester) async {
        int tapCount = 0;

        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {
            tapCount++;
          },
        ));

        // Tap multiple times
        await tester.tap(find.byType(AuthButton));
        await tester.pump();
        await tester.tap(find.byType(AuthButton));
        await tester.pump();
        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        expect(tapCount, equals(3));
      });

      testWidgets('should show splash effect when tapped', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {},
        ));

        // Find the InkWell widget
        expect(find.byType(InkWell), findsOneWidget);

        // Tap and verify InkWell responds
        await tester.tap(find.byType(InkWell));
        await tester.pump();

        // Test passes if no exception is thrown
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty text', (tester) async {
        await tester.pumpWidget(createTestWidget(
          text: '',
          onPressed: () {},
        ));

        expect(find.text(''), findsOneWidget);
        expect(find.byType(AuthButton), findsOneWidget);
      });

      testWidgets('should handle very long text', (tester) async {
        const longText = 'This is a very long button text that might overflow';

        await tester.pumpWidget(createTestWidget(
          text: longText,
          onPressed: () {},
        ));

        expect(find.text(longText), findsOneWidget);
        expect(find.byType(AuthButton), findsOneWidget);
      });

      testWidgets('should handle simultaneous loading and disabled states',
          (tester) async {
        bool buttonPressed = false;

        await tester.pumpWidget(createTestWidget(
          text: 'Login',
          onPressed: () {
            buttonPressed = true;
          },
          isLoading: true,
          isDisabled: true,
        ));

        await tester.tap(find.byType(AuthButton));
        await tester.pump();

        // Should not be pressed due to both loading and disabled states
        expect(buttonPressed, isFalse);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });
  });
}
