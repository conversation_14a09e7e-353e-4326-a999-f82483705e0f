import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:water_metering/core/error/auth_failures.dart';
import 'package:water_metering/domain/services/biometric_service.dart';
import 'package:water_metering/presentation/widgets/auth/biometric_button.dart';
import 'package:water_metering/theme/theme2.dart';

// Mock classes
class MockBiometricService extends Mock implements BiometricService {}

class MockThemeNotifier extends Mock implements ThemeNotifier {}

void main() {
  group('BiometricButton Widget Tests', () {
    late MockBiometricService mockBiometricService;
    late MockThemeNotifier mockThemeNotifier;

    setUpAll(() {
      registerFallbackValue(const BiometricNotAvailableFailure());
    });

    setUp(() {
      mockBiometricService = MockBiometricService();
      mockThemeNotifier = MockThemeNotifier();

      // Register BiometricService in GetIt for tests
      if (!GetIt.instance.isRegistered<BiometricService>()) {
        GetIt.instance.registerLazySingleton<BiometricService>(
            () => mockBiometricService);
      }

      // Setup default biometric service mocks
      when(() => mockBiometricService.isAvailable())
          .thenAnswer((_) async => true);
      when(() => mockBiometricService.isEnrolled())
          .thenAnswer((_) async => true);

      // Setup default theme mock
      when(() => mockThemeNotifier.currentTheme).thenReturn(
        CustomThemeData(
          bgColor: Colors.white,
          loginTitleColor: Colors.black,
          signInColor: Colors.black,
          tableText: Colors.black,
          gridHeadingColor: Colors.black,
          popupcolor: Colors.black,
          noEntriesColor: Colors.grey,
          splashColor: Colors.grey,
          dropDownColor: Colors.white,
          numberWheelSelectedBG: Colors.grey,
          toggleColor: Colors.grey,
          dialogBG: Colors.white,
          dropdownColor: Colors.white,
          bottomNavColor: Colors.white,
          pleaseSignInColor: Colors.black,
          iconColor: Colors.black,
          profileChamferColor: Colors.grey,
          textFieldBGProfile: Colors.white,
          editIconColor: Colors.black,
          calibrateTabBGColor: Colors.white,
          editIconBG: Colors.grey,
          textFieldFillColor: Colors.white,
          onSecondaryContainer: Colors.white,
          inactiveBottomNavbarIconColor: Colors.grey,
          primaryContainer: Colors.white,
          drawerHeadingColor: Colors.black,
          gridLineColor: Colors.grey,
          basicAdvanceTextColor: Colors.blue,
          textfieldTextColor: Colors.black,
          profileBorderColor: Colors.grey,
          textfieldHintColor: Colors.grey,
          headingColor: Colors.black,
          textfieldCursorColor: Colors.blue,
          errorColor: Colors.red,
          primaryColor: Colors.blue,
        ),
      );
    });

    tearDown(() {
      // Clean up GetIt registration
      if (GetIt.instance.isRegistered<BiometricService>()) {
        GetIt.instance.unregister<BiometricService>();
      }
    });

    Widget createTestWidget({
      VoidCallback? onSuccess,
      ValueChanged<String>? onError,
      String? text,
      String reason = 'Please authenticate to continue',
      bool enabled = true,
      bool showWhenUnavailable = true, // Default to true for tests
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) => MultiProvider(
          providers: [
            Provider<BiometricService>.value(value: mockBiometricService),
            ChangeNotifierProvider<ThemeNotifier>.value(
                value: mockThemeNotifier),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: BiometricButton(
                  onSuccess: onSuccess,
                  onError: onError,
                  text: text,
                  reason: reason,
                  enabled: enabled,
                  showWhenUnavailable: showWhenUnavailable,
                ),
              ),
            ),
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render biometric button with default text',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Allow async operations to complete

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.text('LOGIN WITH BIOMETRICS'), findsOneWidget);
        expect(find.byIcon(Icons.fingerprint), findsOneWidget);
      });

      testWidgets('should render custom text when provided', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget(
          text: 'Custom Biometric Text',
        ));
        await tester.pump();

        expect(find.text('Custom Biometric Text'), findsOneWidget);
      });

      testWidgets('should show loading indicator when authenticating',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          // Simulate a delay to test loading state
          await Future.delayed(const Duration(milliseconds: 100));
          return BiometricAuthResult.success();
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap the button to start authentication
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pump(); // Trigger loading state

        // Should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.byIcon(Icons.fingerprint), findsNothing);

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 200));
      });

      testWidgets(
          'should not render when biometrics unavailable and showWhenUnavailable is false',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: false,
        ));
        await tester.pump();

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.byType(GestureDetector), findsNothing);
      });

      testWidgets(
          'should render when biometrics unavailable and showWhenUnavailable is true',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: true,
        ));
        await tester.pump();

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.byType(GestureDetector), findsOneWidget);
      });
    });

    group('Biometric Authentication', () {
      testWidgets('should call onSuccess when authentication succeeds',
          (tester) async {
        bool successCalled = false;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async => BiometricAuthResult.success());

        await tester.pumpWidget(createTestWidget(
          onSuccess: () {
            successCalled = true;
          },
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap the button
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pumpAndSettle(); // Wait for authentication to complete

        expect(successCalled, isTrue);
      });

      testWidgets('should call onError when authentication fails',
          (tester) async {
        String? errorMessage;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async => BiometricAuthResult.failure(
                  const BiometricAuthenticationFailure(),
                ));

        await tester.pumpWidget(createTestWidget(
          onError: (error) {
            errorMessage = error;
          },
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap the button
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pumpAndSettle(); // Wait for authentication to complete

        expect(errorMessage, isNotNull);
        expect(errorMessage, contains('Biometric authentication failed'));
      });

      testWidgets('should handle authentication exceptions', (tester) async {
        String? errorMessage;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenThrow(Exception('Biometric service error'));

        await tester.pumpWidget(createTestWidget(
          onError: (error) {
            errorMessage = error;
          },
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap the button
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pumpAndSettle(); // Wait for error handling to complete

        expect(errorMessage, isNotNull);
        expect(errorMessage, contains('Biometric authentication failed'));
      });

      testWidgets('should pass custom reason to biometric service',
          (tester) async {
        const customReason = 'Custom authentication reason';

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() => mockBiometricService.authenticate(reason: customReason))
            .thenAnswer((_) async => BiometricAuthResult.success());

        await tester.pumpWidget(createTestWidget(
          reason: customReason,
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap the button
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pumpAndSettle(); // Wait for authentication to complete

        // Verify the custom reason was passed
        verify(() => mockBiometricService.authenticate(reason: customReason))
            .called(1);
      });
    });

    group('Button States', () {
      testWidgets('should be disabled when enabled is false', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget(
          enabled: false,
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Button should be rendered but disabled (with reduced opacity)
        expect(find.byType(BiometricButton), findsOneWidget);

        // Tap should not trigger authentication
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pump();

        verifyNever(() =>
            mockBiometricService.authenticate(reason: any(named: 'reason')));
      });

      testWidgets('should be disabled when biometrics not available',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: true,
        ));
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Tap should not trigger authentication
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pump();

        verifyNever(() =>
            mockBiometricService.authenticate(reason: any(named: 'reason')));
      });

      testWidgets('should be disabled during authentication', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return BiometricAuthResult.success();
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Start authentication
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pump();

        // Try to tap again while loading (should tap the container since icon is replaced by progress indicator)
        await tester.tap(find.byType(BiometricButton));
        await tester.pumpAndSettle(); // Wait for authentication to complete

        // Should only call authenticate once
        verify(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .called(1);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle different biometric failure types',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        // Test different failure types
        final failures = [
          const BiometricNotAvailableFailure(),
          const BiometricNotEnrolledFailure(),
          const BiometricAuthenticationFailure(),
          const BiometricCancelledFailure(),
          const UnknownFailure(message: 'Unknown error'),
        ];

        for (final failure in failures) {
          String? errorMessage;

          when(() => mockBiometricService.authenticate(
                  reason: any(named: 'reason')))
              .thenAnswer((_) async => BiometricAuthResult.failure(failure));

          await tester.pumpWidget(createTestWidget(
            onError: (error) {
              errorMessage = error;
            },
          ));
          await tester.pumpAndSettle(); // Wait for initialization to complete

          // Tap the button
          await tester.tap(find.byIcon(Icons.fingerprint));
          await tester.pumpAndSettle(); // Wait for error handling to complete

          expect(errorMessage, isNotNull);
        }
      });
    });

    group('Widget Lifecycle', () {
      testWidgets('should handle widget disposal during authentication',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 200));
          return BiometricAuthResult.success();
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle(); // Wait for initialization to complete

        // Start authentication
        await tester.tap(find.byIcon(Icons.fingerprint));
        await tester.pump();

        // Remove the widget while authentication is in progress
        await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        await tester.pump();

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 300));

        // Should not throw any exceptions
      });
    });

    group('Accessibility', () {
      testWidgets('should be accessible for screen readers', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() => mockBiometricService.isEnrolled())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget(showWhenUnavailable: true));
        await tester.pump();

        // Check that the button has semantic information
        expect(find.byType(GestureDetector), findsOneWidget);
        expect(find.text('LOGIN WITH BIOMETRICS'), findsOneWidget);
      });
    });
  });
}
